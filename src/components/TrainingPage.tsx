import React, { useState } from 'react';
import { Play, Clock, Users, BookOpen, ExternalLink } from 'lucide-react';
import { But<PERSON> } from './Button';

interface TrainingVideo {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  category: string;
  thumbnailUrl: string;
  videoId: string; // YouTube video ID placeholder
}

const trainingVideos: TrainingVideo[] = [
  {
    id: '1',
    title: '🚀 Project Mastery: OKRs for On-Time, On-Budget Construction (JobTread Dashboard Pt. 1)',
    description: 'Stop guessing, start knowing! Part 1 of our "Measure What Matters" series unveils the JobTread Project Delivery Excellence Dashboard. Learn how to set OKRs and track crucial KPIs like on-time completion, budget adherence, and milestone achievement to drive your construction projects to success. Import this dashboard directly into your JobTread account!',
    duration: '12:45',
    difficulty: 'Beginner',
    category: 'Project Management',
    thumbnailUrl: '/api/placeholder/400/225',
    videoId: 'dQw4w9WgXcQ' // Placeholder YouTube ID
  },
  {
    id: '2',
    title: '💰 Boost Your Bottom Line: Construction Financial KPIs & OKRs (JobTread Dashboard Pt. 2)',
    description: 'Is your construction business truly profitable? In Part 2, we dive into the Financial Health & Profitability Dashboard for JobTread. Discover how to use OKRs based on "Measure What Matters" to track gross/net profit margins, cash flow, and AR days. Gain financial clarity and drive healthy growth. Template available for your JobTread account!',
    duration: '15:30',
    difficulty: 'Intermediate',
    category: 'Financial Management',
    thumbnailUrl: '/api/placeholder/400/225',
    videoId: 'dQw4w9WgXcQ'
  },
  {
    id: '3',
    title: '📈 Win More Bids & Grow! Client Success OKRs for Builders (JobTread Dashboard Pt. 3)',
    description: 'Happy clients = more business! Part 3 focuses on the JobTread Client Relationship & Growth Dashboard. We apply "Measure What Matters" principles to help you track client satisfaction, bid win rates, and lead conversion. Build stronger relationships and fuel sustainable growth. Get the JobTread dashboard template now!',
    duration: '11:20',
    difficulty: 'Beginner',
    category: 'Client Management',
    thumbnailUrl: '/api/placeholder/400/225',
    videoId: 'dQw4w9WgXcQ'
  },
  {
    id: '4',
    title: '✅ Zero Harm, Max Quality: Safety & Compliance OKRs in Construction (JobTread Dashboard Pt. 4)',
    description: 'Build it right, build it safe! This episode (Part 4) introduces the Safety, Quality & Compliance Dashboard for JobTread. Learn to set impactful OKRs for safety records (LTIFR), quality audit pass rates, and compliance, inspired by "Measure What Matters." Elevate your standards and protect your reputation. Import into JobTread today!',
    duration: '13:15',
    difficulty: 'Intermediate',
    category: 'Safety & Quality',
    thumbnailUrl: '/api/placeholder/400/225',
    videoId: 'dQw4w9WgXcQ'
  },
  {
    id: '5',
    title: '⚙️ Peak Performance: OKRs for Construction Team & Ops Efficiency (JobTread Dashboard Pt. 5)',
    description: 'Unlock your team\'s potential and streamline operations! In our final part (Pt. 5), explore the JobTread Team & Operational Efficiency Dashboard. We\'ll show you how to use "Measure What Matters" OKRs to track billable ratios, equipment utilization, and subcontractor performance. Drive productivity and build a high-performing team. JobTread template inside!',
    duration: '14:45',
    difficulty: 'Advanced',
    category: 'Operations',
    thumbnailUrl: '/api/placeholder/400/225',
    videoId: 'dQw4w9WgXcQ'
  },
  {
    id: '6',
    title: '🌍 Build from Anywhere: OKRs for the Remote Construction CEO (JobTread Dashboard Pt. 6)',
    description: 'Leading a construction company remotely? This video (Part 6) unpacks how CEOs can leverage the "Measure What Matters" OKR philosophy with a specialized JobTread Dashboard. Discover Key Results for ensuring stellar communication, remote team engagement, project visibility, and process adoption. Learn to scale your construction business effectively, no matter your location. Import the dashboard template and command your business from anywhere!',
    duration: '16:30',
    difficulty: 'Advanced',
    category: 'Leadership',
    thumbnailUrl: '/api/placeholder/400/225',
    videoId: 'dQw4w9WgXcQ'
  }
];

export const TrainingPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedVideo, setSelectedVideo] = useState<TrainingVideo | null>(null);

  const categories = ['all', ...Array.from(new Set(trainingVideos.map(v => v.category)))];

  const filteredVideos = selectedCategory === 'all'
    ? trainingVideos
    : trainingVideos.filter(video => video.category === selectedCategory);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const openVideo = (video: TrainingVideo) => {
    // In a real implementation, this would open a video player modal
    // For now, we'll just set the selected video
    setSelectedVideo(video);
    // Placeholder: Open YouTube video in new tab
    window.open(`https://www.youtube.com/watch?v=${video.videoId}`, '_blank');
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Training Center</h1>
          <p className="text-gray-300 mt-1">
            Master JobTread dashboards with our comprehensive video training series
          </p>
        </div>
        <div className="flex items-center gap-2">
          <BookOpen className="h-5 w-5 text-gray-300" />
          <span className="text-sm text-gray-300">{trainingVideos.length} videos available</span>
        </div>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              selectedCategory === category
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {category === 'all' ? 'All Categories' : category}
          </button>
        ))}
      </div>

      {/* Video Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredVideos.map((video) => (
          <div key={video.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            {/* Video Thumbnail */}
            <div className="relative aspect-video bg-gray-200">
              <img
                src={video.thumbnailUrl}
                alt={video.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                <Button
                  onClick={() => openVideo(video)}
                  variant="primary"
                  icon={<Play className="h-5 w-5" />}
                >
                  Watch Now
                </Button>
              </div>
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                {video.duration}
              </div>
            </div>

            {/* Video Info */}
            <div className="p-4">
              <div className="flex items-start justify-between mb-2">
                <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${getDifficultyColor(video.difficulty)}`}>
                  {video.difficulty}
                </span>
                <span className="text-xs text-gray-500">{video.category}</span>
              </div>

              <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                {video.title}
              </h3>

              <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                {video.description}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex items-center text-xs text-gray-500">
                  <Clock className="h-3 w-3 mr-1" />
                  {video.duration}
                </div>
                <Button
                  onClick={() => openVideo(video)}
                  variant="outline"
                  size="sm"
                  icon={<ExternalLink className="h-3 w-3" />}
                >
                  Watch
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredVideos.length === 0 && (
        <div className="text-center py-12">
          <Play className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No videos found</h3>
          <p className="text-gray-500">Try selecting a different category</p>
        </div>
      )}

      {/* Training Series Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start">
          <BookOpen className="h-6 w-6 text-blue-600 mr-3 mt-1" />
          <div>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              "Measure What Matters" Dashboard Series
            </h3>
            <p className="text-blue-800 mb-4">
              This comprehensive training series is based on John Doerr's "Measure What Matters" methodology,
              adapted specifically for construction businesses using JobTread. Each video includes a downloadable
              dashboard template that you can import directly into your JobTread account.
            </p>
            <div className="flex items-center text-sm text-blue-700">
              <Users className="h-4 w-4 mr-1" />
              <span>Perfect for construction business owners, project managers, and team leaders</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
