import React from 'react';
import { JobTreadConnection } from './JobTreadConnection';
import { InfoCard } from './InfoCard';
import { TestHelper } from './TestHelper';
import { CorsHelper } from './CorsHelper';
import { ApiDebugger } from './ApiDebugger';

interface SettingsPageProps {
  connectionState: {
    isConnected: boolean;
    grantKey?: string;
    organization?: { id: string; name: string };
  };
  onConnectionChange: (isConnected: boolean, grantKey?: string, organization?: { id: string; name: string }) => void;
}

export const SettingsPage: React.FC<SettingsPageProps> = ({ connectionState, onConnectionChange }) => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Settings</h1>
        <p className="text-gray-300">
          Configure your JobTread connection and manage API settings
        </p>
      </div>

      {/* JobTread Connection Section */}
      <div className="bg-gray-700 rounded-lg shadow-lg border border-gray-600 p-6">
        <h2 className="text-xl font-semibold text-white mb-4">JobTread API Connection</h2>
        <JobTreadConnection onConnectionChange={onConnectionChange} />
      </div>

      {/* Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <InfoCard
          title="About Dashboard Sharing"
          content={`
            This tool allows you to export dashboards from one JobTread organization
            and import them into another. It's useful for transferring dashboard designs
            between environments or creating backups.
          `}
        />
        <InfoCard
          title="Limitations"
          content={`
            Dashboards with tiles that reference specific reports, custom fields, or
            data views may not function correctly after import if those entities don't
            exist in the target organization. Role visibility settings are simplified
            in this version.
          `}
          type="warning"
        />
      </div>

      {/* Developer Tools */}
      <div className="space-y-6">
        <div className="border-t border-gray-600 pt-6">
          <h2 className="text-xl font-semibold text-white mb-4">Developer Tools</h2>
          <p className="text-gray-300 mb-6">
            Advanced tools for testing and debugging API connections
          </p>
        </div>

        <TestHelper />
        <CorsHelper />
        <ApiDebugger />
      </div>
    </div>
  );
};
