{"name": "Financial Dashboard", "type": "organization", "tiles": [{"height": 3, "width": 3, "x": 0, "y": 0, "options": {"type": "actionItems"}}, {"height": 3, "width": 5, "x": 3, "y": 0, "options": {"agg": "sum", "name": "Projected Profit", "type": "custom", "fieldId": "projectedProfit", "groupBy": [{"fieldId": "createdAt", "datetimeFormat": "YYYY Q"}], "chartType": "line", "targetType": "job", "thresholds": [{"max": 250000, "color": "#ef4444"}, {"max": 500000, "min": 250000, "color": "#f97316"}, {"min": 500000, "color": "#22c55e"}]}}, {"height": 3, "width": 4, "x": 8, "y": 0, "options": {"agg": "sum", "name": "Cash Flow: Received", "type": "custom", "where": {">": [{"field": ["createdAt"]}, {"datetime": {"fromNow": "P-12W"}}]}, "fieldId": "collected", "groupBy": [{"fieldId": "createdAt", "datetimeFormat": "IYYY IW"}], "chartType": "bar", "targetType": "job"}}, {"height": 3, "width": 4, "x": 8, "y": 3, "options": {"agg": "sum", "name": "Cash Flow: Paid Out", "type": "custom", "where": {">": [{"field": ["createdAt"]}, {"datetime": {"fromNow": "P-12W"}}]}, "fieldId": "paidInvoices", "groupBy": [{"fieldId": "createdAt", "datetimeFormat": "IYYY IW"}], "chartType": "bar", "targetType": "job"}}, {"height": 3, "width": 5, "x": 3, "y": 3, "options": {"agg": "sum", "name": "Invoice Progress", "type": "custom", "where": {">": [{"field": ["createdAt"]}, {"datetime": {"fromNow": "P-12W"}}]}, "fieldId": "amount", "groupBy": [{"fieldId": "status"}], "chartType": "pie", "targetType": "document", "thresholds": [{"max": 250000, "color": "#ef4444"}, {"max": 500000, "min": 250000, "color": "#f97316"}, {"min": 500000, "color": "#22c55e"}]}}, {"height": 3, "width": 5, "x": 3, "y": 6, "options": {"agg": "sum", "name": "Invoice Progress (Copy)", "type": "custom", "where": {">": [{"field": ["createdAt"]}, {"datetime": {"fromNow": "P-12W"}}]}, "fieldId": "amount", "groupBy": [{"fieldId": "status"}], "chartType": "pie", "targetType": "document", "thresholds": [{"max": 250000, "color": "#ef4444"}, {"max": 500000, "min": 250000, "color": "#f97316"}, {"min": 500000, "color": "#22c55e"}]}}, {"height": 6, "width": 3, "x": 0, "y": 3, "options": {"type": "activity"}}], "roles": [], "exportedAt": "2025-05-25T01:51:28.975Z", "sourceOrganizationId": "22NNZt3rjGvv", "exportVersion": "1.0"}